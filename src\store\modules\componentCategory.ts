import { defineStore } from "pinia";

import type {
  ComponentInfoDto,
  ComponentBasicInfoDto,
  ComponentAttributeDto,
  SubcomponentInfoDto,
  ComponentAlarmDto,
  ComponentFunctionDto,
  RenderConditionDto
} from "@/types/componentType";
import { getComponentByID, updateComponent } from "@/api/componentType";
import { isEmpty } from "lodash-es";
export const useComponentCategory = defineStore("componentCategory", {
  state: (): {
    selectedComponent?: ComponentInfoDto;
    selectedComponentId?: number;
    flowInstance?: any;
    // 添加当前编辑功能状态
    currentFunction: ComponentFunctionDto | null;
    // 添加流程图数据状态
    flowData: any;
  } => ({
    selectedComponent: {},
    selectedComponentId: undefined,
    currentFunction: null,
    flowData: null
  }),
  getters: {
    getSelectedId: state => state.selectedComponentId,
    getComponentInfo: state => state.selectedComponent,
    // 基本信息的getter
    getBasicInfo: state => state.selectedComponent.basicInfo || {},
    // 属性列表的getter
    getAttributes: state => state.selectedComponent.attributes || [],
    // 子组件信息的getter
    getSubcomponents: state => state.selectedComponent.subcomponents || [],
    // 告警信息的getter
    getAlarms: state => state.selectedComponent.alarms || [],
    // 函数信息的getter
    getFunctions: state => state.selectedComponent.functions || [],
    // 渲染条件的getter
    getRenderConditions: state => state.selectedComponent.renderCondition || [],
    // 获取组件名称
    getComponentName: state => state.selectedComponent.basicInfo?.name || "",
    // 获取组件版本
    getComponentVersion: state =>
      state.selectedComponent.basicInfo?.version || "",
    getFlowInstance: state => state.flowInstance
  },
  // TODO: 支持节点多条边的逻辑
  actions: {
    setFlowInstance(instance: any) {
      this.flowInstance = instance;
    },
    async fetchComponentInfo(componentId: string) {
      try {
        const initComponentData = {
          basicInfo: {
            id: "",
            guid: "",
            name: "",
            version: "",
            versionDescription: "",
            description: "",
            tags: [],
            createTime: "",
            author: ""
          },
          attributes: [],
          subcomponents: [],
          alarms: [],
          functions: [],
          renderCondition: []
        };
        const response = await getComponentByID(componentId);
        Object.assign(initComponentData, response);
        this.selectedComponentId = componentId;
        this.setComponentInfo(initComponentData);
      } catch (error) {
        console.error('获取组件详情失败:', error);
        throw new Error('Failed to get component details');
      }
    },
    setComponentInfo(data: ComponentInfoDto) {
      this.selectedComponent = data;
    },
    // 设置基本信息
    setBasicInfo(basicInfo: ComponentBasicInfoDto) {
      this.selectedComponent.basicInfo = basicInfo;
    },
    // 更新基本信息的特定字段
    updateBasicInfo(basicInfo: Partial<ComponentBasicInfoDto>) {
      this.selectedComponent.basicInfo = { ...basicInfo };
    },
    // 设置属性列表
    setAttributes(attributes: ComponentAttributeDto[]) {
      this.selectedComponent.attributes = attributes;
    },
    // 添加属性
    addAttribute(attribute: ComponentAttributeDto) {
      if (!this.selectedComponent.attributes) {
        this.selectedComponent.attributes = [];
      }
      this.selectedComponent.attributes.push(attribute);
    },
    // 删除属性
    removeAttribute(attributeName: string) {
      if (!this.selectedComponent.attributes) return;
      this.selectedComponent.attributes =
        this.selectedComponent.attributes.filter(
          attr => attr.name !== attributeName
        );
    },
    // 设置子组件列表
    setSubcomponents(subcomponents: SubcomponentInfoDto[]) {
      this.selectedComponent.subcomponents = subcomponents;
    },
    // 添加子组件
    addSubcomponent(subcomponent: SubcomponentInfoDto) {
      if (!this.selectedComponent.subcomponents) {
        this.selectedComponent.subcomponents = [];
      }
      this.selectedComponent.subcomponents.push(subcomponent);
    },
    // 设置告警列表
    setAlarms(alarms: ComponentAlarmDto[]) {
      this.selectedComponent.alarms = alarms;
    },
    // 添加告警
    addAlarm(alarm: ComponentAlarmDto) {
      if (!this.alarms) this.alarms = [];
      this.selectedComponent.alarms.push(alarm);
    },
    // 设置函数列表
    setFunctions(functions: ComponentFunctionDto[]) {
      this.selectedComponent.functions = functions;
    },
    // 添加函数
    addFunction(func: ComponentFunctionDto) {
      if (!this.functions) this.functions = [];
      this.selectedComponent.functions.push(func);
    },
    // 设置渲染条件
    setRenderConditions(renderConditions: RenderConditionDto[]) {
      this.selectedComponent.renderCondition = renderConditions;
    },
    async updateCurrentComponent(data: Partial<ComponentInfoDto>) {
      try {
        // 先更新store中的数据
        if (!this.selectedComponent) {
          return {
            success: false,
            message: "没有选中的组件"
          };
        }

        // 更新store中的数据
        Object.keys(data).forEach(key => {
          this.selectedComponent[key] = data[key];
        });

        await updateComponent(this.selectedComponent);
        await this.fetchComponentInfo(this.selectedComponentId);
        return { success: true };
      } catch (error) {
        console.error("更新组件失败:", error);
        return { success: false, message: error.message };
      }
    },
    // 重置所有状态
    resetComponentInfo() {
      this.$reset();
    },
    // 设置当前编辑的功能
    setCurrentFunction(func: ComponentFunctionDto | null) {
      this.currentFunction = func ? { ...func } : null;
      // 解析流程数据
      if (func?.flowData) {
        try {
          this.flowData = JSON.parse(func.flowData);
        } catch (error) {
          console.error("解析流程数据失败:", error);
          this.flowData = null;
        }
      } else {
        this.flowData = null;
      }
    },
    // 更新流程数据
    updateFlowData(data: any) {
      this.flowData = data;
      if (this.currentFunction) {
        this.currentFunction.flowData = JSON.stringify(data);
      }
    },
    addFlowNode(node: any) {
      if (!this.flowData || isEmpty(this.flowData)) {
        this.flowData = { nodes: [], edges: [] };
      }
      this.flowInstance?.addNodes([node]);
      this.flowData.nodes.push(node);
    },
    // 更新节点关系
    updateNodeRelation(source: string, target: string, type: "add" | "remove") {
      if (!this.flowData) return;
      if (type === "add") {
        this.flowData.edges.push({ source, target });
      } else {
        this.flowData.edges = this.flowData.edges.filter(
          edge => edge.source !== source && edge.target !== target
        );
      }
    },
    // 更新节点配置
    updateNodeConfig(nodeId: string, config: Record<string, any>) {
      if (!this.flowData) return;

      const nodeIndex = this.flowData.nodes.findIndex(
        (node: any) => node.id === nodeId
      );
      if (nodeIndex >= 0) {
        const configValue = this.flowData.nodes[nodeIndex].data.configValue;
        this.flowData.nodes[nodeIndex].data.configValue = {
          ...configValue,
          [config.name]: config
        };
      }
    },
    // 保存当前功能到组件
    async saveCurrentFunction() {
      if (!this.currentFunction || !this.selectedComponent) {
        return { success: false, message: "没有可保存的功能" };
      }

      try {
        // 确保流程数据同步到当前功能
        if (this.flowData) {
          const instanceDataInfo = this.flowInstance.toObject();
          // 将 this.flowData.nodes 中的 data 值同步到 instanceDataInfo.nodes 中
          instanceDataInfo.nodes.forEach(instanceNode => {
            // 查找 flowData 中对应 ID 的节点
            const flowNode = this.flowData.nodes.find(
              (n: any) => n.id === instanceNode.id
            );
            if (flowNode) {
              // 将 flowNode 的 data 值赋给 instanceNode 的 data 值
              instanceNode.data = flowNode.data;
            }
          });
          this.currentFunction.flowData = JSON.stringify(instanceDataInfo);

          // 更新flowNodes
          this.currentFunction.flowNodes = this.flowData.nodes.map(
            (node: any) => {
              // 计算nextNodeIds
              const nextNodeIds = this.flowData.edges
                .filter((edge: any) => edge.source === node.id)
                .map((edge: any) => edge.target);

              // 计算previousNodeIds
              const previousNodeIds = this.flowData.edges
                .filter((edge: any) => edge.target === node.id)
                .map((edge: any) => edge.source);

              // 计算当前节点的nextNodeIdMap
              const nextNodeIdMap = {};
              this.flowData.edges
                .filter((edge: any) => edge.source === node.id)
                .forEach((edge: any) => {
                  if (
                    edge.sourceHandle &&
                    edge.sourceHandle.startsWith("source-")
                  ) {
                    const handleName = edge.sourceHandle.replace("source-", "");
                    nextNodeIdMap[handleName] = edge.target;
                  }
                });

              // 从configValue获取配置值
              const basicConfigList = node.data.configValue || {};
              const basicConfig = Object.values(basicConfigList);

              return {
                id: node.id,
                description: node.data.description || "",
                templateName: node.data.name || "",
                nextNodeIds,
                previousNodeIds,
                nextNodeIdMap,
                basicConfig
              };
            }
          );
        }

        // 查找并更新功能
        const updatedFunctions = [...(this.selectedComponent.functions || [])];
        const functionIndex = updatedFunctions.findIndex(
          func => func.name === this.currentFunction?.name
        );

        console.log("updatedFunctions", this.currentFunction.flowNodes);

        if (functionIndex >= 0) {
          updatedFunctions[functionIndex] = this.currentFunction;
        } else {
          updatedFunctions.push(this.currentFunction);
        }

        // 调用更新组件接口
        const response = await this.updateCurrentComponent({
          functions: updatedFunctions
        });

        return response;
      } catch (error) {
        console.error("保存功能失败:", error);
        return { success: false, message: "保存功能失败" };
      }
    }
  }
});
