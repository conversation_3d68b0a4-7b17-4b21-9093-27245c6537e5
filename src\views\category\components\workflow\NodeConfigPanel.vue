<template>
  <div class="node-config-panel">
    <template v-if="selectedNodeId">
      <!-- 整合的节点标题和基础配置区域 -->
      <div class="panel-section">
        <NodeHeader :node="nodeData" @delete-node="handleDeleteNode" />

        <div class="config-form-container">
          <div class="section-label">
            <el-icon><Setting /></el-icon>
            <span>基础配置</span>
          </div>

          <div class="config-form-content">
            <NodeBasicConfig
              :key="selectedNode.id"
              v-model="nodeConfig"
              :current-node-id="selectedNode.id"
              labelWidth="80"
              :node-type="nodeData.component"
              @config-change="handleConfigChange"
            />
          </div>
        </div>
      </div>

      <!-- 参数区域 - 使用卡片风格替代表格 -->
      <NodeOutputConfig
        :removeNodes="removeNodes"
        :selectedNode="selectedNode"
      />
    </template>

    <el-empty
      v-else
      class="empty-state"
      description="请选择一个节点进行配置"
      :image-size="100"
    >
      <template #image>
        <div class="empty-image">
          <el-icon :size="40" color="#909399"><Select /></el-icon>
        </div>
      </template>
      <div class="empty-tips">
        <p>从流程图中选择一个节点</p>
        <p>或从左侧工具箱拖拽一个新节点到画布</p>
      </div>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
/**
 * 节点配置面板组件
 *
 * 该组件用于配置流程图中的节点属性，包括基本配置和输出参数管理。
 * 对于开始节点，会自动映射当前功能的输入参数作为输出参数。
 *
 * @component NodeConfigPanel
 */

// 导入依赖，按功能分组
import { ref, computed, defineProps, defineEmits, defineExpose } from "vue";
import { ElMessage } from "element-plus";
import { cloneDeep } from "lodash-es";
import { Setting, Select } from "@element-plus/icons-vue";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 导入子组件
import NodeHeader from "./NodeHeader.vue";
import NodeBasicConfig from "./NodeBasicConfig.vue";
import NodeOutputConfig from "./NodeOutputConfig.vue";

// 组件props定义
const props = defineProps<{
  removeNodes: (nodeIds: string[]) => void;
}>();

const emit = defineEmits<{
  (
    e: "update-node-config",
    payload: { nodeId: string; config: Record<string, any> }
  ): void;
}>();

// 使用Store
const componentStore = useComponentCategory();

// 组件状态
const selectedNodeId = ref<string | null>(null);
const nodeConfig = ref<Record<string, any>>({});

// 计算属性
const selectedNode = computed(() => {
  if (selectedNodeId.value) {
    const { findNode } = componentStore.getFlowInstance;
    const node = findNode(selectedNodeId.value);
    return node;
  }
  return null;
});

const nodeData = computed(() => selectedNode.value?.data || {});

// 当节点配置变更时
const handleConfigChange = (newConfig: Record<string, any>) => {
  if (selectedNodeId.value) {
    // 更新本地 nodeConfig
    nodeConfig.value[newConfig.name] = newConfig;

    const { updateNodeData } = componentStore.getFlowInstance;
    updateNodeData(selectedNodeId.value, nodeConfig.value);

    // 将配置变更发给父组件
    emit("update-node-config", {
      nodeId: selectedNodeId.value,
      config: newConfig
    });
  }
};

// 方法：删除节点
const handleDeleteNode = async () => {
  if (!selectedNodeId.value) return;

  try {
    props.removeNodes([selectedNodeId.value]);
    componentStore.flowData.nodes = componentStore.flowData.nodes.filter(
      (node: any) => node.id !== selectedNodeId.value
    );
    selectedNodeId.value = null;

    ElMessage.success({
      message: "节点已删除",
      duration: 1500
    });
  } catch (error) {
    console.log("用户取消删除节点");
  }
};

const updateSelectedNodeId = (nodeId: string | null) => {
  if (!nodeId) {
    selectedNodeId.value = null;
    nodeConfig.value = {};
    return;
  }

  // 无论是否相同节点，都重置并更新配置
  selectedNodeId.value = nodeId;
  // reset

  // 获取节点的当前配置值
  const node = componentStore.flowData.nodes.find(
    (node: any) => node.id === nodeId
  );

  console.log("setSelectedNodeId", node);
  nodeConfig.value = cloneDeep(
    node?.data?.configValue || node?.data?.initialValue || {}
  );
};

// 暴露方法
defineExpose({
  setSelectedNodeId: (nodeId: string | null) => {
    updateSelectedNodeId(nodeId);
  }
});
</script>

<style scoped>
.node-config-panel {
  height: 100%;
  padding: 16px;
  overflow-y: auto;
  background-color: var(--el-bg-color-page, #f5f7fa);
  position: relative;
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
}

.panel-section {
  margin-bottom: 20px;
  border-radius: 8px;
  animation: fade-in 0.3s ease-in-out;
}

.section-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-primary);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 16px;
  position: relative;
}

.section-label::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, var(--el-border-color-light), transparent);
}

.section-label .el-icon {
  color: var(--el-color-primary);
}

.config-form-container {
  background-color: var(--el-bg-color, #fff);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition:
    transform 0.2s,
    box-shadow 0.3s ease;
  margin-top: 12px;
}

.config-form-container:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

.config-form-content {
  padding: 12px 0;
}

/* 自定义滚动条 */
.node-config-panel::-webkit-scrollbar {
  width: 6px;
}

.node-config-panel::-webkit-scrollbar-track {
  background: transparent;
}

.node-config-panel::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.node-config-panel::-webkit-scrollbar-thumb:hover {
  background-color: var(--el-border-color-darker);
}

.empty-state {
  padding-top: 120px;
}
</style>
