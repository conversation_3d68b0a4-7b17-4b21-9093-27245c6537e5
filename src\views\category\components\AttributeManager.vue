<template>
  <div class="attribute-manager">
    <div class="header flex justify-between items-center mb-4">
      <h2 class="text-lg font-medium">属性管理</h2>
      <el-button type="primary" @click="handleAddAttribute">
        <el-icon class="mr-1"><Plus /></el-icon>添加属性
      </el-button>
    </div>

    <el-table :data="attributes" border style="width: 100%">
      <el-table-column prop="name" label="名称" min-width="120" />
      <el-table-column prop="displayName" label="显示名称" min-width="120" />
      <el-table-column prop="type" label="值类型" min-width="100">
        <template #default="{ row }">
          <span>{{
            valueType.find(item => item.value === row.type)?.label
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="unit" label="单位" min-width="80" />
      <el-table-column prop="defaultValue" label="默认值" min-width="100">
        <template #default="{ row }">
          <span>{{ formatDefaultValue(row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEditAttribute(row)">
            编辑
          </el-button>
          <el-button type="danger" link @click="handleDeleteAttribute(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 属性编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑属性' : '添加属性'"
      width="650px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="attributeForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="attributeForm.name" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="显示名称" prop="displayName">
          <el-input v-model="attributeForm.displayName" />
        </el-form-item>
        <el-form-item label="值类型" prop="type">
          <el-select
            v-model="attributeForm.type"
            class="w-full"
            @change="handleTypeChange"
          >
            <el-option
              v-for="item in valueType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="attributeForm.description"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="attributeForm.unit" />
        </el-form-item>
        <el-form-item label="默认值" prop="defaultValue">
          <el-input
            v-if="attributeForm.type === 3"
            v-model="attributeForm.defaultValue"
          />
          <el-input-number
            v-else-if="attributeForm.type === 1"
            v-model="attributeForm.defaultValue"
            :controls="false"
            class="w-full"
          />
          <el-input-number
            v-else-if="attributeForm.type === 2"
            v-model="attributeForm.defaultValue"
            :controls="false"
            :precision="2"
            class="w-full"
          />
          <el-select
            v-else-if="attributeForm.type === 4"
            v-model="attributeForm.defaultValue"
            class="w-full"
          >
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
          <el-input
            v-else
            v-model="attributeForm.defaultValue"
            placeholder="请输入默认值"
          />
        </el-form-item>

        <el-divider>限制规则</el-divider>

        <el-form-item
          v-if="attributeForm.type === 1 || attributeForm.type === 2"
          label="上限"
          prop="limitRule.upperLimit"
        >
          <el-input v-model="attributeForm.limitRule.upperLimit" />
        </el-form-item>
        <el-form-item
          v-if="attributeForm.type === 1 || attributeForm.type === 2"
          label="下限"
          prop="limitRule.lowerLimit"
        >
          <el-input v-model="attributeForm.limitRule.lowerLimit" />
        </el-form-item>
        <el-form-item
          v-if="attributeForm.type === 3"
          label="长度限制"
          prop="limitRule.lengthLimit"
        >
          <el-input v-model="attributeForm.limitRule.lengthLimit" />
        </el-form-item>
        <el-form-item
          v-if="attributeForm.type === 3"
          label="正则表达式"
          prop="limitRule.regex"
        >
          <el-input v-model="attributeForm.limitRule.regex" />
        </el-form-item>
        <el-form-item
          v-if="attributeForm.type === 2"
          label="小数位数"
          prop="limitRule.decimalDigits"
        >
          <el-input-number
            v-model="attributeForm.limitRule.decimalDigits"
            :min="0"
            :max="10"
            :controls="false"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="是否持久化" prop="isPersistent">
          <el-switch v-model="attributeForm.isPersistent" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAttribute">保存</el-button>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除属性" width="400px">
      <p>确定要删除属性 "{{ currentAttribute?.name }}" 吗？此操作不可恢复。</p>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定删除</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import type { ComponentAttributeDto } from "@/types/componentType";
import { valueType } from "@/const/enums";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 获取Pinia store
const componentStore = useComponentCategory();

// 从store获取属性
const attributes = computed(
  () => componentStore.selectedComponent?.attributes || []
);

// 对话框可见性
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);

// 是否是编辑模式
const isEdit = ref(false);

// 当前操作的属性
const currentAttribute = ref<ComponentAttributeDto>();

// 表单引用
const formRef = ref<FormInstance>();

// 属性表单数据
const attributeForm = reactive<{
  name: string;
  displayName: string;
  type: number;
  description: string;
  unit: string;
  defaultValue: any;
  isPersistent: boolean;
  limitRule: {
    upperLimit?: string;
    lowerLimit?: string;
    lengthLimit?: string;
    regex?: string;
    decimalDigits?: number;
  };
}>({
  name: "",
  displayName: "",
  type: 1, // 默认为字符串类型
  description: "",
  unit: "",
  defaultValue: "",
  isPersistent: true,
  limitRule: {
    upperLimit: "",
    lowerLimit: "",
    lengthLimit: "",
    regex: "",
    decimalDigits: 0
  }
});

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入属性名称", trigger: "blur" },
    {
      pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
      message: "属性名只能包含字母、数字和下划线，且必须以字母开头",
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        // 如果是编辑模式，跳过自身名称的检查
        if (isEdit.value && currentAttribute.value?.name === value) {
          callback();
          return;
        }

        // 检查是否与现有属性名称重复
        const existingNames = attributes.value.map(attr => attr.name);
        if (existingNames.includes(value)) {
          callback(new Error("属性名称已存在，请使用其他名称"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  displayName: [{ required: true, message: "请输入显示名称", trigger: "blur" }],
  valueType: [{ required: true, message: "请选择值类型", trigger: "change" }]
});

// 格式化默认值显示
const formatDefaultValue = (row: ComponentAttributeDto) => {
  if (row.type === 4) {
    return row.defaultValue ? "是" : "否";
  } else if (row.type === 5 || row.type === 6) {
    try {
      return JSON.stringify(row.defaultValue);
    } catch (error) {
      return row.defaultValue;
    }
  } else {
    return row.defaultValue;
  }
};

// 处理添加属性
const handleAddAttribute = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 处理编辑属性
const handleEditAttribute = (row: ComponentAttributeDto) => {
  isEdit.value = true;
  currentAttribute.value = row;

  // 填充表单数据
  attributeForm.name = row.name;
  attributeForm.displayName = row.displayName;
  attributeForm.type = row.type;
  attributeForm.description = row.description;
  attributeForm.unit = row.unit;
  attributeForm.defaultValue = row.defaultValue;
  attributeForm.isPersistent = true; // 默认为真
  // 处理限制规则
  attributeForm.limitRule = row.limitRule;

  dialogVisible.value = true;
};

// 处理删除属性
const handleDeleteAttribute = (row: ComponentAttributeDto) => {
  currentAttribute.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (!currentAttribute.value) return;

  try {
    // 移除要删除的属性
    const updatedAttributes = attributes.value.filter(
      attr => attr.name !== currentAttribute.value?.name
    );

    // 使用Pinia store更新组件
    const response = await componentStore.updateCurrentComponent({
      attributes: updatedAttributes
    });

    if (response.success) {
      ElMessage.success("删除成功");
      deleteDialogVisible.value = false;
    } else {
      ElMessage.error(`删除失败: ${response.message}`);
    }
  } catch (error) {
    ElMessage.error("删除失败");
    console.error("删除属性失败:", error);
  }
};

// 保存属性
const saveAttribute = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        // 构造属性数据
        const attributeData: ComponentAttributeDto = {
          name: attributeForm.name,
          displayName: attributeForm.displayName,
          type: attributeForm.type,
          description: attributeForm.description,
          unit: attributeForm.unit,
          defaultValue: attributeForm.defaultValue?.toString(),
          isPersistent: attributeForm.isPersistent,
          limitRule: attributeForm.limitRule
        };

        let response;
        if (isEdit.value) {
          // 更新属性 - 使用Pinia store
          // 需要获取当前组件属性列表并更新特定属性
          const updatedAttributes = [...attributes.value];
          const attributeIndex = updatedAttributes.findIndex(
            attr => attr.name === attributeForm.name
          );

          if (attributeIndex >= 0) {
            updatedAttributes[attributeIndex] = attributeData;
          } else {
            updatedAttributes.push(attributeData);
          }

          // 调用store更新组件
          response = await componentStore.updateCurrentComponent({
            attributes: updatedAttributes
          });

          if (response.success) {
            ElMessage.success("更新成功");
            dialogVisible.value = false;
          } else {
            ElMessage.error(`更新失败: ${response.message}`);
          }
        } else {
          // 添加属性 - 使用Pinia store
          const updatedAttributes = [...attributes.value, attributeData];

          // 调用store更新组件
          response = await componentStore.updateCurrentComponent({
            attributes: updatedAttributes
          });

          if (response.success) {
            ElMessage.success("添加成功");
            dialogVisible.value = false;
          } else {
            ElMessage.error(`添加失败: ${response.message}`);
          }
        }
      } catch (error) {
        ElMessage.error(isEdit.value ? "更新失败" : "添加失败");
        console.error(isEdit.value ? "更新属性失败:" : "添加属性失败:", error);
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  attributeForm.name = "";
  attributeForm.displayName = "";
  attributeForm.type = 3; // 默认为字符串类型 (3 = String)
  attributeForm.description = "";
  attributeForm.unit = "";
  attributeForm.defaultValue = "";
  attributeForm.isPersistent = true;
  attributeForm.limitRule = {
    upperLimit: "",
    lowerLimit: "",
    lengthLimit: "",
    regex: "",
    decimalDigits: 0
  };

  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 处理类型变化
const handleTypeChange = (newType: number) => {
  // 更新valueType（兼容旧版）
  switch (newType) {
    case 0: // Unknown
      attributeForm.defaultValue = "";
      break;
    case 1: // Int
      attributeForm.defaultValue = 0;
      break;
    case 2: // Double
      attributeForm.defaultValue = 0.0;
      break;
    case 3: // String
      attributeForm.defaultValue = "";
      break;
    case 4: // Bool
      attributeForm.defaultValue = false;
      break;
    case 5: // Array
      attributeForm.defaultValue = [];
      break;
    case 6: // Object
      attributeForm.defaultValue = {};
      break;
    default:
      attributeForm.defaultValue = "";
  }

  // 重置限制规则
  attributeForm.limitRule = {
    upperLimit: "",
    lowerLimit: "",
    lengthLimit: "",
    regex: "",
    decimalDigits: 0
  };
};
</script>

<style scoped lang="scss">
.attribute-manager {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
</style>
