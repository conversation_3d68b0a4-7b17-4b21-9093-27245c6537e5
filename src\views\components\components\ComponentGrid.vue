<template>
  <div class="component-grid-container">
    <!-- 工具栏 -->
    <div class="grid-toolbar">
      <div class="sort-controls">
        <span class="control-label">排序:</span>
        <el-select
          v-model="sortBy"
          placeholder="排序方式"
          size="small"
          class="sort-select"
        >
          <el-option label="名称" value="name" />
          <el-option label="创建时间" value="createTime" />
          <el-option label="版本" value="version" />
          <el-option label="作者" value="author" />
        </el-select>
        <el-button size="small" class="sort-order-btn" @click="toggleSortOrder">
          <el-icon>
            <component :is="sortOrder === 'asc' ? ArrowUp : ArrowDown" />
          </el-icon>
          {{ sortOrder === "asc" ? "升序" : "降序" }}
        </el-button>
      </div>
    </div>

    <!-- 网格布局 -->
    <div class="component-grid">
      <div
        v-for="component in sortedComponents"
        :key="component.id"
        class="component-card"
        @dblclick="handleCardDoubleClick(component)"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="header-left">
            <div class="component-icon">
              <el-icon><Document /></el-icon>
            </div>

            <div class="component-info">
              <h3 class="component-name" :title="component.name">
                {{ component.name }}
              </h3>
              <div class="component-version">v{{ component.version }}</div>
            </div>
          </div>
          <div class="card-actions">
            <el-button
              size="small"
              type="primary"
              text
              title="编辑"
              @click.stop="handleEdit(component)"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button
              size="small"
              type="success"
              text
              title="复制"
              @click.stop="handleCopy(component)"
            >
              <el-icon><CopyDocument /></el-icon>
            </el-button>
            <el-dropdown trigger="click" @command="handleAction">
              <el-button size="small" circle text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{ action: 'delete', component }">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="component-tags">
            <el-tag
              v-for="tag in component.tags.slice(0, 2)"
              :key="tag"
              size="small"
              :type="getTagType(tag)"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <el-tag
              v-if="component.tags.length > 2"
              size="small"
              type="info"
              class="more-tags"
            >
              +{{ component.tags.length - 2 }}
            </el-tag>
          </div>

          <div class="component-description" :title="component.description">
            {{ component.description || "暂无描述" }}
          </div>
        </div>

        <!-- 卡片底部 -->
        <div class="card-footer">
          <div class="create-info">
            <span class="author">{{ component.author || "未知" }}</span>
            <span class="create-time">{{
              formatDate(component.createTime, "short")
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="components.length === 0" class="empty-state">
      <el-empty description="暂无组件数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  Document,
  MoreFilled,
  Edit,
  CopyDocument,
  Delete,
  Check,
  ArrowUp,
  ArrowDown
} from "@element-plus/icons-vue";
import type { ComponentListItem } from "@/types/componentType";
import { formatDate, sortComponents } from "../utils/componentUtils";

interface Props {
  components: ComponentListItem[];
}

interface Emits {
  (e: "open", component: ComponentListItem): void;
  (e: "copy", component: ComponentListItem): void;
  (e: "delete", component: ComponentListItem): void;
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

// 排序状态
const sortBy = ref("name");
const sortOrder = ref<"asc" | "desc">("asc");

// 标签类型映射
const tagTypeMap: Record<
  string,
  "primary" | "success" | "warning" | "danger" | "info"
> = {
  Robot: "primary",
  LoadPort: "success",
  Buffer: "warning",
  Chamber: "danger",
  EFEM: "info"
};

const getTagType = (
  tag: string
): "primary" | "success" | "warning" | "danger" | "info" | undefined => {
  return tagTypeMap[tag];
};

// 排序后的组件列表
const sortedComponents = computed(() => {
  return sortComponents(props.components, sortBy.value, sortOrder.value);
});

// 切换排序顺序
const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === "asc" ? "desc" : "asc";
};

// 卡片双击处理
const handleCardDoubleClick = (component: ComponentListItem) => {
  emit("open", component);
};

// 编辑处理
const handleEdit = (component: ComponentListItem) => {
  emit("open", component);
};

// 复制处理
const handleCopy = (component: ComponentListItem) => {
  emit("copy", component);
};

// 下拉菜单操作处理
const handleAction = ({
  action,
  component
}: {
  action: string;
  component: ComponentListItem;
}) => {
  switch (action) {
    case "delete":
      emit("delete", component);
      break;
  }
};
</script>

<style scoped lang="scss">
.component-grid-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.grid-toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;

  .sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;

    .control-label {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }

    .sort-select {
      width: 120px;
    }

    .sort-order-btn {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.component-grid {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  align-content: start;
}

.component-card {
  position: relative;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 220px;
  display: flex;
  flex-direction: column;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
    overflow: hidden;
    flex: 1;

    .component-icon {
      flex-shrink: 0;
      width: 36px;
      height: 36px;
      background: var(--el-color-primary-light-9);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--el-color-primary);
      font-size: 18px;
    }

    .component-info {
      overflow: hidden;

      .component-name {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.2;
      }

      .component-version {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }

  .card-actions {
    flex-shrink: 0;
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.component-card:hover .card-actions {
  opacity: 1;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow: hidden;

  .component-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;

    .tag-item {
      margin: 0;
    }

    .more-tags {
      margin: 0;
    }
  }

  .component-description {
    font-size: 13px;
    color: #606266;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.card-footer {
  margin-top: auto;
  padding-top: 8px;

  .create-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #909399;

    .author {
      font-weight: 500;
      color: #606266;
    }

    .create-time {
      color: #909399;
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 响应式调整
@media (max-width: 768px) {
  .component-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    padding: 12px;
  }

  .grid-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .view-controls,
    .sort-controls {
      justify-content: center;
    }
  }
}
</style>
