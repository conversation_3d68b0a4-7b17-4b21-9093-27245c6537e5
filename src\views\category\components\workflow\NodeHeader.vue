<template>
  <div v-if="node" class="node-header">
    <div class="header-content">
      <div class="header-text">
        <h3 class="header-title">{{ nodeType }}</h3>
        <p v-if="description" class="header-description">
          {{ description }}
        </p>
      </div>
    </div>
    <div class="header-actions">
      <el-tooltip content="删除此节点" :show-after="300">
        <el-button
          type="danger"
          :icon="Delete"
          class="delete-button"
          @click="confirmDelete"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from "vue";
import { Delete } from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
import { nodeConfigInfo } from "../flowNode";

export default defineComponent({
  name: "NodeHeader",
  props: {
    node: {
      type: Object,
      required: true
    }
  },
  emits: ["delete-node"],
  setup(props, { emit }) {
    const nodeType = computed(() => nodeConfigInfo[props.node.component].name);
    const description = computed(
      () => nodeConfigInfo[props.node.component].description
    );

    const confirmDelete = async () => {
      try {
        await ElMessageBox.confirm(
          "删除节点将同时删除该节点的所有连接，确定要删除吗？",
          "删除确认",
          {
            confirmButtonText: "确定删除",
            cancelButtonText: "取消",
            type: "warning",
            confirmButtonClass: "el-button--danger",
            closeOnClickModal: false
          }
        );
        // TODO: 擅长 store 中此ID节点
        emit("delete-node");
      } catch {
        // 用户取消删除
      }
    };

    return {
      nodeType,
      description,
      confirmDelete,
      Delete
    };
  }
});
</script>

<style scoped>
.node-header {
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: box-shadow 0.3s ease;
}

.node-header:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}
.header-text {
  display: flex;
  flex-direction: column;
}

.header-title {
  margin: 0;
  padding: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.5;
}

.header-description {
  margin: 4px 0 0 0;
  padding: 0;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.delete-button {
  transition: all 0.2s ease;
}

.delete-button:hover {
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .node-header {
    flex-direction: column;
    gap: a0px;
  }

  .header-actions {
    width: 100%;
    margin-top: 16px;
    justify-content: flex-end;
  }
}
</style>
