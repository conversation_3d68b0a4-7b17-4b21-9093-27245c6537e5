// const { VITE_HIDE_HOME } = import.meta.env;
const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Home",
  component: Layout,
  redirect: "/components",
  meta: {
    icon: "ep:home-filled",
    title: "首页",
    rank: 0
  },
  children: [
    // {
    //   path: "/home",
    //   name: "CategoryIndex",
    //   component: () => import("@/views/category/index.vue"),
    //   meta: {
    //     title: "零件库",
    //     showLink: true
    //   }
    // }
    {
      path: "/components",
      name: "ComponentList",
      component: () => import("@/views/components/index.vue"),
      meta: {
        title: "零件列表",
        showLink: true
      }
    },
    {
      path: "/components/:id",
      name: "ComponentDetail",
      component: () => import("@/views/components/detail.vue"),
      meta: {
        title: "零件详情",
        showLink: false
      }
    }
  ]
} satisfies RouteConfigsTable;
