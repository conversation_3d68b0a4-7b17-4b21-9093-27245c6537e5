<template>
  <div class="component-table-container">
    <el-table
      ref="tableRef"
      :data="components"
      :height="tableHeight"
      stripe
      border
      @row-dblclick="handleRowDoubleClick"
      @sort-change="handleSortChange"
    >
      <!-- 名称列 -->
      <el-table-column
        prop="name"
        label="名称"
        sortable="custom"
        width="200"
        fixed="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="name-cell">
            <el-icon class="component-icon"><Document /></el-icon>
            <span class="component-name">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <!-- 版本列 -->
      <el-table-column
        prop="version"
        label="版本"
        sortable="custom"
        width="120"
        show-overflow-tooltip
      />

      <!-- 标签列 -->
      <el-table-column
        prop="tags"
        label="标签"
        width="200"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="tags-cell">
            <el-tag
              v-for="tag in row.tags"
              :key="tag"
              size="small"
              class="tag-item"
              :type="getTagType(tag)"
            >
              {{ tag }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 描述列 -->
      <el-table-column
        prop="description"
        label="描述"
        min-width="200"
        show-overflow-tooltip
      />

      <!-- 创建时间列 -->
      <el-table-column
        prop="createTime"
        label="创建时间"
        sortable="custom"
        width="180"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>

      <!-- 作者列 -->
      <el-table-column
        prop="author"
        label="作者"
        width="120"
        show-overflow-tooltip
      />

      <!-- 操作列 -->
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              size="small"
              type="primary"
              link
              @click="editComponent(row)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="info"
              link
              @click="copyComponent(row)"
            >
              复制
            </el-button>
            <el-button
              size="small"
              type="danger"
              link
              @click="deleteComponent(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from "vue";
import { Document } from "@element-plus/icons-vue";
import type { ComponentListItem } from "@/types/componentType";
import { formatDate } from "../utils/componentUtils";

interface Props {
  components: ComponentListItem[];
}

interface Emits {
  (e: "open", component: ComponentListItem): void;
  (e: "sort-change", sort: { prop: string; order: string }): void;
  (e: "copy", component: ComponentListItem): void;
  (e: "delete", component: ComponentListItem): void;
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const tableRef = ref();

// 表格高度
const tableHeight = computed(() => "calc(100vh - 300px)");

// 标签类型映射
const tagTypeMap: Record<
  string,
  "primary" | "success" | "warning" | "danger" | "info"
> = {
  Robot: "primary",
  LoadPort: "success",
  Buffer: "warning",
  Chamber: "danger",
  EFEM: "info"
};

const getTagType = (
  tag: string
): "primary" | "success" | "warning" | "danger" | "info" | undefined => {
  return tagTypeMap[tag];
};

// 双击行处理
const handleRowDoubleClick = (row: ComponentListItem) => {
  emit("open", row);
};

// 排序变化处理
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  emit("sort-change", { prop, order });
};

// 操作方法
const editComponent = (component: ComponentListItem) => {
  emit("open", component);
};

const copyComponent = (component: ComponentListItem) => {
  emit("copy", component);
};

const deleteComponent = (component: ComponentListItem) => {
  emit("delete", component);
};
</script>

<style scoped lang="scss">
.component-table-container {
  height: 100%;

  :deep(.el-table) {
    .name-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .component-icon {
        color: var(--el-color-primary);
        font-size: 16px;
      }

      .component-name {
        font-weight: 500;
        color: #303133;
      }
    }

    .tags-cell {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .tag-item {
        margin: 0;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;

      .el-button {
        padding: 0;
        margin: 0;
        height: auto;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    // 表格行样式
    .el-table__row {
      cursor: pointer;

      &:hover {
        background-color: var(--el-table-row-hover-bg-color);
      }
    }

    // 表头样式
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 600;
      }
    }

    // 选中行样式
    .el-table__row.current-row {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

// 响应式调整
@media (max-width: 1200px) {
  .component-table-container {
    :deep(.el-table) {
      .action-buttons {
        flex-direction: column;
        gap: 2px;
      }
    }
  }
}
</style>
