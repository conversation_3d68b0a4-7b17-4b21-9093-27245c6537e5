import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { getAllComponent } from "@/api/componentType";
import type { ComponentListItem } from "@/types/componentType";

export function useComponentList() {
  // 响应式数据
  const componentList = ref<ComponentListItem[]>([]);
  const searchKeyword = ref("");
  const typeFilter = ref("");
  const availableTags = ref<string[]>([]);
  const loading = ref(false);

  // 高级筛选条件
  const advancedFilters = ref({
    name: "",
    tags: [] as string[],
    dateRange: null as [Date, Date] | null,
    author: "",
    version: ""
  });

  // 过滤后的组件列表
  const filteredComponentList = computed(() => {
    let result = componentList.value;

    // 基础搜索筛选
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      result = result.filter(item => {
        return (
          item.name.toLowerCase().includes(keyword) ||
          item.tags.some(tag => tag.toLowerCase().includes(keyword)) ||
          item.version.toLowerCase().includes(keyword)
        );
      });
    }

    // 标签筛选
    if (typeFilter.value) {
      const filterValue = typeFilter.value.toLowerCase();
      result = result.filter(item =>
        item.tags.some(tag => tag.toLowerCase() === filterValue)
      );
    }

    // 高级筛选
    if (advancedFilters.value.name) {
      const nameFilter = advancedFilters.value.name.toLowerCase();
      result = result.filter(item =>
        item.name.toLowerCase().includes(nameFilter)
      );
    }

    if (advancedFilters.value.tags.length > 0) {
      result = result.filter(item =>
        advancedFilters.value.tags.some(tag => item.tags.includes(tag))
      );
    }

    if (advancedFilters.value.version) {
      const versionFilter = advancedFilters.value.version.toLowerCase();
      result = result.filter(item =>
        item.version.toLowerCase().includes(versionFilter)
      );
    }

    return result;
  });

  // 按分类分组的组件列表
  const groupedComponentList = computed(() => {
    const grouped: {
      name: string;
      isFolder: true;
      children: ComponentListItem[];
    }[] = [];
    const tagGroups: Record<string, ComponentListItem[]> = {};

    // 将组件按标签分组
    filteredComponentList.value.forEach(item => {
      item.tags.forEach(tag => {
        if (!tagGroups[tag]) {
          tagGroups[tag] = [];
        }
        tagGroups[tag].push(item);
      });
    });

    // 创建分类文件夹
    Object.entries(tagGroups).forEach(([tag, items]) => {
      grouped.push({
        name: tag,
        isFolder: true,
        children: items
      });
    });

    return grouped;
  });

  // 刷新组件列表
  const refreshList = async () => {
    loading.value = true;
    try {
      let components: any[] = [];

      // 如果 localStorage 中没有数据，从 API 获取
      try {
        const response = await getAllComponent();
        components = response.map(comp => ({
          id: comp.basicInfo.id || null,
          name: comp.basicInfo.name,
          tags: comp.basicInfo.tags || [],
          version: comp.basicInfo.version,
          createTime: comp.basicInfo.createTime,
          author: comp.basicInfo.author,
          description: comp.basicInfo.description,
          isFolder: false
        }));

        // 保存到 localStorage
        localStorage.setItem("componentList", JSON.stringify(components));
      } catch (apiError) {
        console.warn("API 获取失败，使用默认数据:", apiError);
        // 使用默认的模拟数据
        components = [];
      }

      componentList.value = components;

      // 收集所有可用标签
      const tagSet = new Set<string>();
      components.forEach(comp => {
        comp.tags.forEach(tag => tagSet.add(tag));
      });
      availableTags.value = Array.from(tagSet).sort();
    } catch (error) {
      ElMessage.error("获取零件列表失败");
      console.error("获取零件列表失败:", error);
    } finally {
      loading.value = false;
    }
  };

  // 搜索组件
  const searchComponents = (keyword: string) => {
    searchKeyword.value = keyword;
  };

  // 按标签筛选
  const filterByTag = (tag: string) => {
    typeFilter.value = tag;
  };

  // 应用高级筛选
  const applyAdvancedFilters = (filters: any) => {
    advancedFilters.value = { ...filters };
  };

  // 清除筛选
  const clearFilters = () => {
    searchKeyword.value = "";
    typeFilter.value = "";
    advancedFilters.value = {
      name: "",
      tags: [],
      dateRange: null,
      author: "",
      version: ""
    };
  };

  // 获取组件统计信息
  const getStatistics = computed(() => {
    const total = componentList.value.length;
    const filtered = filteredComponentList.value.length;
    const tagStats = availableTags.value.map(tag => ({
      tag,
      count: componentList.value.filter(comp => comp.tags.includes(tag)).length
    }));

    return {
      total,
      filtered,
      tagStats
    };
  });

  // 排序选项
  const sortOptions = [
    { label: "名称", value: "name" },
    { label: "创建时间", value: "createTime" },
    { label: "版本", value: "version" },
    { label: "作者", value: "author" }
  ];

  const sortBy = ref("name");
  const sortOrder = ref<"asc" | "desc">("asc");

  // 排序后的组件列表
  const sortedComponentList = computed(() => {
    const list = [...filteredComponentList.value];

    list.sort((a, b) => {
      let aValue = a[sortBy.value];
      let bValue = b[sortBy.value];

      // 处理日期类型
      if (sortBy.value === "createTime") {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // 处理字符串类型
      if (typeof aValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder.value === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return list;
  });

  // 设置排序
  const setSorting = (field: string, order: "asc" | "desc" = "asc") => {
    sortBy.value = field;
    sortOrder.value = order;
  };

  return {
    // 数据
    componentList,
    searchKeyword,
    typeFilter,
    availableTags,
    loading,
    advancedFilters,

    // 计算属性
    filteredComponentList,
    filteredComponents: filteredComponentList, // 添加别名
    groupedComponentList,
    sortedComponentList,
    getStatistics,

    // 排序
    sortOptions,
    sortBy,
    sortOrder,
    setSorting,

    // 方法
    refreshList,
    searchComponents,
    filterByTag,
    applyAdvancedFilters,
    clearFilters
  };
}
