<template>
  <div class="function-manager">
    <div class="header flex justify-between items-center mb-4">
      <h2 class="text-lg font-medium">功能列表</h2>
      <el-button type="primary" @click="handleAddFunction">
        <el-icon class="mr-1"><Plus /></el-icon>添加功能
      </el-button>
    </div>

    <el-table :data="functions" border style="width: 100%">
      <el-table-column prop="name" label="功能名称" min-width="120" />
      <el-table-column label="输入参数" min-width="120">
        <template #default="{ row }">
          <el-tag
            v-for="param in row.inputArgs"
            :key="param.name"
            class="mr-1 mb-1"
          >
            {{ param.name }}
          </el-tag>
          <span v-if="!row.inputArgs || row.inputArgs.length === 0">无</span>
        </template>
      </el-table-column>
      <el-table-column label="输出参数" min-width="120">
        <template #default="{ row }">
          <el-tag
            v-for="param in row.outputArgs"
            :key="param.name"
            class="mr-1 mb-1"
            type="success"
          >
            {{ param.name }}
          </el-tag>
          <span v-if="!row.outputArgs || row.outputArgs.length === 0">无</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="description"
        label="描述"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column label="流程节点" min-width="100">
        <template #default="{ row }">
          <span>{{ row.flowNodes?.length || 0 }} 个节点</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEditFunction(row)">
            编辑
          </el-button>
          <el-button type="success" link @click="handleEditWorkflow(row)">
            流程编排
          </el-button>
          <el-button type="danger" link @click="handleDeleteFunction(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 功能编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑功能' : '添加功能'"
      width="650px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="functionForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="功能名称" prop="name">
          <el-input v-model="functionForm.name" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="functionForm.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-divider>输入参数</el-divider>

        <div
          v-for="(param, index) in functionForm.inputArgs"
          :key="index"
          class="mb-4 p-3 border border-gray-200 rounded-md"
        >
          <div class="flex justify-end mb-2">
            <el-button
              type="danger"
              size="small"
              circle
              @click="removeParameter(index, 'input')"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <el-form-item
            :label="'参数名'"
            :prop="`inputArgs.${index}.name`"
            :rules="{
              required: true,
              message: '请输入参数名',
              trigger: 'blur'
            }"
          >
            <el-input v-model="param.name" />
          </el-form-item>
          <el-form-item
            :label="'参数类型'"
            :prop="`inputArgs.${index}.type`"
            :rules="{
              required: true,
              message: '请输入参数类型',
              trigger: 'blur'
            }"
          >
            <el-select v-model="param.type" class="w-full">
              <el-option
                v-for="item in valueType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="'参数注释'"
            :prop="`inputArgs.${index}.comment`"
          >
            <el-input v-model="param.description" />
          </el-form-item>
        </div>
        <div class="flex justify-center mt-4">
          <el-button type="primary" plain @click="addParameter('input')">
            <el-icon class="mr-1"><Plus /></el-icon>添加输入参数
          </el-button>
        </div>

        <el-divider>输出参数</el-divider>

        <div
          v-for="(param, index) in functionForm.outputArgs"
          :key="index"
          class="mb-4 p-3 border border-gray-200 rounded-md"
        >
          <div class="flex justify-end mb-2">
            <el-button
              type="danger"
              size="small"
              circle
              @click="removeParameter(index, 'output')"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <el-form-item
            :label="'参数名'"
            :prop="`outputArgs.${index}.name`"
            :rules="{
              required: true,
              message: '请输入参数名',
              trigger: 'blur'
            }"
          >
            <el-input v-model="param.name" />
          </el-form-item>
          <el-form-item
            :label="'参数类型'"
            :prop="`outputArgs.${index}.type`"
            :rules="{
              required: true,
              message: '请输入参数类型',
              trigger: 'blur'
            }"
          >
            <el-select v-model="param.type" class="w-full">
              <el-option
                v-for="item in valueType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="'参数注释'"
            :prop="`outputArgs.${index}.comment`"
          >
            <el-input v-model="param.description" />
          </el-form-item>
        </div>
        <div class="flex justify-center mt-4">
          <el-button type="success" plain @click="addParameter('output')">
            <el-icon class="mr-1"><Plus /></el-icon>添加输出参数
          </el-button>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveFunction">保存</el-button>
      </template>
    </el-dialog>

    <!-- 工作流编辑对话框 -->
    <el-dialog
      v-model="workflowDialogVisible"
      title="流程编排"
      width="90%"
      top="5vh"
      :fullscreen="true"
      destroy-on-close
    >
      <div class="workflow-container">
        <WorkflowEditor
          v-if="workflowDialogVisible"
          @save="saveWorkflow"
          @cancel="workflowDialogVisible = false"
        />
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除功能" width="400px">
      <p>确定要删除功能 "{{ currentFunction?.name }}" 吗？此操作不可恢复。</p>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定删除</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
// import type { ComponentFunction } from "../types";
import type {
  ComponentFunctionDto,
  FlowNodeTemplateArgDesDto
} from "@/types/componentType";
import WorkflowEditor from "./workflow/WorkflowEditor.vue";
import { valueType } from "@/const/enums";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 从store获取数据
const componentStore = useComponentCategory();
const functions = computed(
  () => componentStore.selectedComponent?.functions || []
);

// 对话框可见性
const dialogVisible = ref(false);
const workflowDialogVisible = ref(false);
const deleteDialogVisible = ref(false);

// 是否是编辑模式
const isEdit = ref(false);

// 当前操作的功能
const currentFunction = ref<ComponentFunctionDto | null>(null);

// 表单引用
const formRef = ref<FormInstance>();

// 功能表单数据
const functionForm = reactive<{
  name: string;
  description: string;
  inputArgs: FlowNodeTemplateArgDesDto[];
  outputArgs: FlowNodeTemplateArgDesDto[];
}>({
  name: "",
  description: "",
  inputArgs: [],
  outputArgs: []
});

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入功能名称", trigger: "blur" },
    {
      pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
      message: "功能名只能包含字母、数字和下划线，且必须以字母开头",
      trigger: "blur"
    }
  ],
  description: [{ required: true, message: "请输入功能说明", trigger: "blur" }]
});

// 处理添加功能
const handleAddFunction = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 处理编辑功能
const handleEditFunction = (row: ComponentFunctionDto) => {
  isEdit.value = true;
  currentFunction.value = row;

  // 填充表单数据
  functionForm.name = row.name;
  functionForm.description = row.description;
  functionForm.inputArgs = row.inputArgs || [];
  functionForm.outputArgs = row.outputArgs || [];
  dialogVisible.value = true;
};

// 处理编辑工作流
const handleEditWorkflow = (row: ComponentFunctionDto) => {
  try {
    // 确保功能有flowData属性
    const functionData: ComponentFunctionDto = {
      ...row,
      flowData: row.flowData || "{}"
    };

    // 设置当前编辑的功能到store
    componentStore.setCurrentFunction(functionData);

    // 打开工作流对话框
    workflowDialogVisible.value = true;
  } catch (error) {
    console.error("处理工作流数据错误:", error);
  }
};

// 处理删除功能
const handleDeleteFunction = (row: ComponentFunctionDto) => {
  currentFunction.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (!currentFunction.value) return;

  try {
    // 移除要删除的功能
    const updatedFunctions = functions.value.filter(
      func => func.name !== currentFunction.value?.name
    );
    const response = await componentStore.updateCurrentComponent({
      functions: updatedFunctions
    });

    if (response.success) {
      ElMessage.success("删除成功");
      deleteDialogVisible.value = false;
    } else {
      ElMessage.error(`删除失败: ${response.message}`);
    }
  } catch (error) {
    ElMessage.error("删除失败");
    console.error("删除功能失败:", error);
  }
};

// 添加参数
const addParameter = (type: "input" | "output") => {
  if (type === "input") {
    functionForm.inputArgs.push({
      name: "",
      type: 0,
      description: ""
    });
  } else {
    functionForm.outputArgs.push({
      name: "",
      type: 0,
      description: ""
    });
  }
};

// 移除参数
const removeParameter = (index: number, type: "input" | "output") => {
  if (type === "input") {
    functionForm.inputArgs.splice(index, 1);
  } else {
    functionForm.outputArgs.splice(index, 1);
  }
};

// 保存功能
const saveFunction = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        // 构造功能数据
        const functionData: ComponentFunctionDto = {
          name: functionForm.name,
          description: functionForm.description,
          inputArgs: functionForm.inputArgs || [],
          outputArgs: functionForm.outputArgs || [],
          flowNodes: currentFunction.value?.flowNodes || [],
          flowData: currentFunction.value?.flowData || "{}"
        };

        let response;
        if (isEdit.value) {
          // 需要获取当前组件功能列表并更新特定功能
          const updatedFunctions = [...functions.value];
          const functionIndex = updatedFunctions.findIndex(
            func => func.name === functionForm.name
          );

          if (functionIndex >= 0) {
            updatedFunctions[functionIndex] = functionData;
          } else {
            updatedFunctions.push(functionData);
          }

          // 调用store更新组件
          response = await componentStore.updateCurrentComponent({
            functions: updatedFunctions
          });
        } else {
          // 添加功能 - 使用Pinia store
          const updatedFunctions = [...functions.value, functionData];

          // 调用store更新组件
          response = await componentStore.updateCurrentComponent({
            functions: updatedFunctions
          });
        }

        if (response.success) {
          ElMessage.success(isEdit.value ? "更新成功" : "添加成功");
          dialogVisible.value = false;
        } else {
          ElMessage.error(
            `${isEdit.value ? "更新" : "添加"}失败: ${response.message}`
          );
        }
      } catch (error) {
        ElMessage.error(isEdit.value ? "更新失败" : "添加失败");
        console.error(isEdit.value ? "更新功能失败:" : "添加功能失败:", error);
      }
    }
  });
};

// 保存工作流
const saveWorkflow = async (data: any) => {
  // 已经在WorkflowEditor组件内部通过store处理
  ElMessage.success("流程保存成功");
  // 可选：关闭对话框
  workflowDialogVisible.value = false;
};

// 重置表单
const resetForm = () => {
  functionForm.name = "";
  functionForm.description = "";
  functionForm.inputArgs = [];
  functionForm.outputArgs = [];

  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields();
  }
};
</script>

<style scoped lang="scss">
.function-manager {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.workflow-container {
  height: calc(100vh - 120px);
  overflow: hidden;
}

.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 4px;
}

.w-full {
  width: 100%;
}
</style>
